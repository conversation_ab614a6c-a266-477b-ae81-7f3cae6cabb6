#!/usr/bin/env python3
"""
Test just Pattern 1 which should work
"""

import sys
import pandas as pd
import json
sys.path.append('src')

from backtesting_rule_parser import BacktestingRuleParser
from behavioral_intelligence import generate_orb_timeframes

def test_pattern_1():
    """Test Pattern 1 which should generate signals"""
    print("🧪 Testing Pattern 1: London Session High-Pressure Breakout")
    print("=" * 70)
    
    # Load and prepare data
    print("📊 Loading data...")
    data = pd.read_csv('data/2025.6.23DEUIDXEUR_M1_UTCPlus01-M1-No Session.csv')
    data['DateTime'] = pd.to_datetime(data['DateTime'], format='%Y%m%d %H:%M:%S')
    data.set_index('DateTime', inplace=True)
    
    # Generate ORB timeframes
    orb_timeframes = generate_orb_timeframes(data)
    test_data = orb_timeframes['15min']  # Pattern 1 uses 15min
    
    print(f"   Test data: {len(test_data)} bars")
    print(f"   ORB UP signals available: {test_data['orb_breakout_up'].sum()}")
    print(f"   London session bars: {len(test_data[(test_data['hour'] >= 9) & (test_data['hour'] < 18)])}")
    
    # Pattern 1 from the LLM response
    pattern_1 = {
        "pattern_name": "London Session High-Pressure Breakout",
        "description": "Exploits institutional buying pressure and retail FOMO after London opening range breakout",
        "entry_conditions": [
            {
                "condition": "orb_breakout_above",
                "orb_period_minutes": 30,
                "orb_period_bars": 3
            },
            {
                "condition": "session_filter",
                "value": "london"
            }
        ],
        "entry_logic": "AND",
        "exit_conditions": [
            {
                "condition": "fixed_pips_stop",
                "pips": 25
            },
            {
                "condition": "fixed_pips_target",
                "pips": 75
            }
        ]
    }
    
    print(f"\n🔧 Testing pattern: {pattern_1['pattern_name']}")
    print(f"   Entry conditions: {len(pattern_1['entry_conditions'])}")
    for i, cond in enumerate(pattern_1['entry_conditions']):
        print(f"      {i+1}. {cond['condition']}: {cond.get('value', 'N/A')}")
    
    # Test with rule parser
    parser = BacktestingRuleParser()
    
    # Test signal generation on London session bars
    signal_count = 0
    london_bars = test_data[(test_data['hour'] >= 9) & (test_data['hour'] < 18)]
    test_bars = min(1000, len(london_bars))
    
    print(f"\n🔍 Testing signal generation on first {test_bars} London session bars...")
    
    london_indices = london_bars.index[:test_bars]
    
    for i, timestamp in enumerate(london_indices):
        if i < 10:  # Skip first few bars for history
            continue
            
        try:
            # Get the actual index in the full dataset
            full_idx = test_data.index.get_loc(timestamp)
            
            # Test individual conditions first
            orb_condition = parser._orb_breakout_above(test_data, full_idx, {})
            session_condition = parser._session_filter(test_data, full_idx, {"value": "london"})
            
            if orb_condition and session_condition:
                signal_count += 1
                if signal_count <= 5:  # Show first 5 signals
                    close_price = test_data['Close'].iloc[full_idx]
                    orb_high = test_data['opening_range_high'].iloc[full_idx]
                    hour = test_data['hour'].iloc[full_idx]
                    print(f"   ✅ Signal {signal_count}: {timestamp}, Hour={hour}, Close={close_price:.2f}, ORB_High={orb_high:.2f}")
                    print(f"      ORB condition: {orb_condition}, Session condition: {session_condition}")
                    
        except Exception as e:
            if signal_count == 0 and i < 20:  # Only show first few errors
                print(f"   ❌ Error at bar {i}: {e}")
    
    print(f"\n📊 Results:")
    print(f"   Total signals generated: {signal_count}/{test_bars}")
    print(f"   Signal rate: {signal_count/test_bars*100:.2f}%")
    
    if signal_count > 0:
        print(f"\n🎉 SUCCESS! Pattern 1 should generate {signal_count} signals")
        print("   The issue is likely with other patterns using invalid conditions")
    else:
        print(f"\n❌ FAILED! Even Pattern 1 is not generating signals")
        print("   Need to debug further...")
        
        # Debug the individual conditions
        print(f"\n🔍 Debugging individual conditions...")
        sample_idx = 100
        if sample_idx < len(test_data):
            orb_result = parser._orb_breakout_above(test_data, sample_idx, {})
            session_result = parser._session_filter(test_data, sample_idx, {"value": "london"})
            hour = test_data['hour'].iloc[sample_idx]
            orb_signal = test_data['orb_breakout_up'].iloc[sample_idx]
            
            print(f"   Sample bar {sample_idx}:")
            print(f"      Hour: {hour}")
            print(f"      ORB signal in data: {orb_signal}")
            print(f"      ORB condition result: {orb_result}")
            print(f"      Session condition result: {session_result}")
    
    return signal_count > 0

if __name__ == "__main__":
    success = test_pattern_1()
    if success:
        print("\n🎉 Pattern 1 test PASSED!")
    else:
        print("\n❌ Pattern 1 test FAILED!")
