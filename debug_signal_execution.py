#!/usr/bin/env python3
"""
Debug signal generation and trade execution in the backtester
"""

import sys
import pandas as pd
import json
sys.path.append('src')

from backtesting_rule_parser import BacktestingRuleParser
from behavioral_intelligence import generate_orb_timeframes
from backtesting_rule_parser import SchemaBasedPatternParser

def debug_signal_execution():
    """Debug the actual signal generation and trade execution"""
    print("🔍 DEBUG: Signal Generation and Trade Execution")
    print("=" * 70)
    
    # Load the latest LLM session to get the exact patterns
    with open('llm_data/DEUIDXEUR/session_20250702_205511.json', 'r') as f:
        session_data = json.load(f)
    
    # Parse the LLM response to get the patterns
    parser = SchemaBasedPatternParser()
    patterns = parser.parse_llm_response(session_data['llm_analysis'])
    
    print(f"📊 Loaded {len(patterns)} patterns from LLM session")
    
    # Load and prepare data
    data = pd.read_csv('data/2025.6.23DEUIDXEUR_M1_UTCPlus01-M1-No Session.csv')
    data['DateTime'] = pd.to_datetime(data['DateTime'], format='%Y%m%d %H:%M:%S')
    data.set_index('DateTime', inplace=True)
    
    # Generate ORB timeframes
    orb_timeframes = generate_orb_timeframes(data)
    test_data = orb_timeframes['15min']  # Most patterns use 15min
    
    print(f"📊 Test data: {len(test_data)} bars")
    print(f"   ORB UP signals: {test_data['orb_breakout_up'].sum()}")
    print(f"   ORB DOWN signals: {test_data['orb_breakout_down'].sum()}")
    print(f"   London session bars: {len(test_data[(test_data['hour'] >= 9) & (test_data['hour'] < 18)])}")
    
    # Test Pattern 1 (should work based on our previous test)
    pattern_1 = patterns[0]
    print(f"\n🔧 Testing Pattern 1: {pattern_1.pattern_name}")
    print(f"   Entry conditions: {len(pattern_1.entry_conditions)}")
    for i, cond in enumerate(pattern_1.entry_conditions):
        print(f"      {i+1}. {cond}")
    
    # Create rule parser
    rule_parser = BacktestingRuleParser()
    
    # Test using the EXACT same method as the backtester
    print(f"\n🔍 Testing using backtester's pattern function generation...")

    # Generate the Python function exactly like the backtester does
    # Enable debug logging in the parser
    rule_parser._debug_logging = True
    pattern_function = rule_parser._create_python_function(pattern_1)

    if pattern_function is None:
        print("❌ Failed to create pattern function")
        return

    print("✅ Pattern function created successfully")

    # Test the function on London session bars specifically
    signal_count = 0
    london_bars = test_data[(test_data['hour'] >= 9) & (test_data['hour'] < 18)]
    london_indices = [test_data.index.get_loc(ts) for ts in london_bars.index[:100]]  # First 100 London bars

    print(f"🔍 Testing pattern function on London session bars...")
    print(f"   London session bars available: {len(london_bars)}")
    print(f"   Testing first {len(london_indices)} London bars...")

    for i in london_indices[:10]:  # Test first 10 London session bars
        try:
            print(f"\n   🔧 Testing bar {i}: {test_data.index[i]}")

            # Call the pattern function exactly like the backtester does
            signal = pattern_function(test_data, i)

            if signal:
                signal_count += 1
                timestamp = test_data.index[i]
                close_price = test_data['Close'].iloc[i]
                orb_high = test_data['opening_range_high'].iloc[i]
                hour = test_data['hour'].iloc[i]

                print(f"   ✅ Signal {signal_count}: {timestamp}")
                print(f"      Hour={hour}, Close={close_price:.2f}, ORB_High={orb_high:.2f}")
                print(f"      Signal details: {signal}")
            else:
                print(f"      ❌ No signal returned")

        except Exception as e:
            print(f"   ❌ Error at bar {i}: {e}")
            import traceback
            traceback.print_exc()
            break
    
    print(f"\n📊 Manual Signal Results:")
    print(f"   Signals found: {signal_count}/{len(london_indices[:10])}")
    
    if signal_count > 0:
        print(f"\n✅ SUCCESS! Signals are being generated")
        print(f"   The issue might be in the backtester's trade execution")
    else:
        print(f"\n❌ PROBLEM! No signals being generated")
        print(f"   Need to debug the entry condition evaluation")
        
        # Debug the first few bars in detail
        print(f"\n🔍 Detailed debugging of first few bars...")
        london_bars = test_data[(test_data['hour'] >= 9) & (test_data['hour'] < 18)]
        for i in range(10, min(15, len(london_bars))):
            timestamp = london_bars.index[i]
            full_idx = test_data.index.get_loc(timestamp)
            
            print(f"\n   Bar {i}: {timestamp}")
            print(f"      Hour: {test_data['hour'].iloc[full_idx]}")
            print(f"      Close: {test_data['Close'].iloc[full_idx]:.2f}")
            print(f"      ORB High: {test_data['opening_range_high'].iloc[full_idx]:.2f}")
            print(f"      ORB breakout signal: {test_data['orb_breakout_up'].iloc[full_idx]}")
            
            # Test each condition individually
            try:
                orb_result = rule_parser._orb_breakout_above(test_data, full_idx, {})
                session_result = rule_parser._session_filter(test_data, full_idx, {"value": "london"})
                print(f"      ORB condition: {orb_result}")
                print(f"      Session condition: {session_result}")
                print(f"      Combined (AND): {orb_result and session_result}")
            except Exception as e:
                print(f"      Error testing conditions: {e}")

if __name__ == "__main__":
    debug_signal_execution()
