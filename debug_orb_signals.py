#!/usr/bin/env python3
"""
Debug script to check ORB signal generation
"""

import sys
import pandas as pd
import numpy as np
sys.path.append('src')

from behavioral_intelligence import generate_orb_timeframes

def debug_orb_signals():
    """Debug ORB signal generation"""
    print("🔍 DEBUG: ORB Signal Generation")
    print("=" * 60)
    
    # Load raw data
    print("📊 Loading raw data...")
    data = pd.read_csv('data/2025.6.23DEUIDXEUR_M1_UTCPlus01-M1-No Session.csv')
    print(f"   Raw data: {len(data)} rows")
    print(f"   Columns: {list(data.columns)}")
    print(f"   Date range: {data.iloc[0, 0]} to {data.iloc[-1, 0]}")
    
    # Convert DateTime column to proper datetime index
    data['DateTime'] = pd.to_datetime(data['DateTime'], format='%Y%m%d %H:%M:%S')
    data.set_index('DateTime', inplace=True)
    
    print(f"   Datetime index: {data.index[0]} to {data.index[-1]}")
    print(f"   Sample hours: {data.index[:10].hour.tolist()}")
    
    # Generate ORB timeframes
    print("\n🔄 Generating ORB timeframes...")
    orb_timeframes = generate_orb_timeframes(data)
    
    # Check 15min timeframe (most common)
    timeframe = '15min'
    if timeframe in orb_timeframes:
        df = orb_timeframes[timeframe]
        print(f"\n📊 Analyzing {timeframe} timeframe:")
        print(f"   Rows: {len(df)}")
        print(f"   Columns: {list(df.columns)}")
        
        # Check ORB-specific columns
        orb_columns = [col for col in df.columns if 'orb' in col.lower() or 'opening' in col.lower()]
        print(f"   ORB columns: {orb_columns}")
        
        # Check for ORB signals
        if 'orb_breakout_up' in df.columns:
            breakout_up_count = df['orb_breakout_up'].sum()
            print(f"   ORB breakout UP signals: {breakout_up_count}")
            
            if breakout_up_count > 0:
                # Show first few breakout signals
                breakout_bars = df[df['orb_breakout_up'] == True].head(5)
                print(f"   First 5 breakout UP bars:")
                for idx, row in breakout_bars.iterrows():
                    print(f"      {idx}: Close={row['Close']:.5f}, ORB_High={row.get('opening_range_high', 'N/A')}")
        
        if 'orb_breakout_down' in df.columns:
            breakout_down_count = df['orb_breakout_down'].sum()
            print(f"   ORB breakout DOWN signals: {breakout_down_count}")
        
        # Check opening range data
        if 'opening_range_high' in df.columns:
            valid_orb_high = df['opening_range_high'].notna().sum()
            print(f"   Valid opening_range_high values: {valid_orb_high}/{len(df)} ({valid_orb_high/len(df)*100:.1f}%)")
            
            if valid_orb_high > 0:
                sample_orb = df[df['opening_range_high'].notna()].head(3)
                print(f"   Sample ORB ranges:")
                for idx, row in sample_orb.iterrows():
                    print(f"      {idx}: High={row.get('opening_range_high', 'N/A'):.5f}, Low={row.get('opening_range_low', 'N/A'):.5f}")
        
        # Check session information
        if 'session_candle_number' in df.columns:
            max_session_candle = df['session_candle_number'].max()
            print(f"   Max session candle number: {max_session_candle}")
            
            # Show session distribution
            session_counts = df['session_candle_number'].value_counts().head(10)
            print(f"   Session candle distribution (top 10): {dict(session_counts)}")
        
        # Check hour distribution
        if 'hour' in df.columns:
            hour_counts = df['hour'].value_counts().sort_index()
            print(f"   Hour distribution: {dict(hour_counts.head(10))}")
            
            # Check specific session hours
            london_hours = df[(df['hour'] >= 9) & (df['hour'] < 18)]
            ny_hours = df[(df['hour'] >= 15) & (df['hour'] < 22)]
            asian_hours = df[(df['hour'] >= 1) & (df['hour'] <= 7)]
            
            print(f"   London session bars (9-18h): {len(london_hours)}")
            print(f"   NY session bars (15-22h): {len(ny_hours)}")
            print(f"   Asian session bars (1-7h): {len(asian_hours)}")

if __name__ == "__main__":
    debug_orb_signals()
