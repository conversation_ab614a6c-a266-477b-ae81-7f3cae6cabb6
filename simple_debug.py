#!/usr/bin/env python3
"""
Simple debug to test pattern function on specific London session bars
"""

import sys
import pandas as pd
import json
sys.path.append('src')

from backtesting_rule_parser import SchemaBasedPatternParser
from behavioral_intelligence import generate_orb_timeframes

def simple_debug():
    """Simple test of pattern function"""
    print("🔍 SIMPLE DEBUG: Pattern Function Test")
    print("=" * 50)
    
    # Load the latest LLM session
    with open('llm_data/DEUIDXEUR/session_20250702_205511.json', 'r') as f:
        session_data = json.load(f)
    
    # Parse patterns
    parser = SchemaBasedPatternParser()
    patterns = parser.parse_llm_response(session_data['llm_analysis'])
    pattern_1 = patterns[0]
    
    print(f"📊 Pattern: {pattern_1.pattern_name}")
    print(f"   Entry conditions: {pattern_1.entry_conditions}")
    
    # Load data
    data = pd.read_csv('data/2025.6.23DEUIDXEUR_M1_UTCPlus01-M1-No Session.csv')
    data['DateTime'] = pd.to_datetime(data['DateTime'], format='%Y%m%d %H:%M:%S')
    data.set_index('DateTime', inplace=True)
    
    # Generate ORB timeframes
    orb_timeframes = generate_orb_timeframes(data)
    test_data = orb_timeframes['15min']
    
    print(f"📊 Data loaded: {len(test_data)} bars")
    
    # Find a specific London session bar that should have signals
    london_bars = test_data[(test_data['hour'] >= 9) & (test_data['hour'] < 18)]
    orb_signal_bars = london_bars[london_bars['orb_breakout_up'] == True]
    
    print(f"📊 London session bars: {len(london_bars)}")
    print(f"📊 ORB signal bars in London session: {len(orb_signal_bars)}")
    
    if len(orb_signal_bars) > 0:
        # Test the first ORB signal bar
        test_timestamp = orb_signal_bars.index[0]
        test_idx = test_data.index.get_loc(test_timestamp)
        
        print(f"\n🔧 Testing specific bar: {test_timestamp} (index {test_idx})")
        print(f"   Hour: {test_data['hour'].iloc[test_idx]}")
        print(f"   Close: {test_data['Close'].iloc[test_idx]:.2f}")
        print(f"   ORB High: {test_data['opening_range_high'].iloc[test_idx]:.2f}")
        print(f"   ORB breakout signal: {test_data['orb_breakout_up'].iloc[test_idx]}")
        
        # Test individual conditions
        orb_result = parser._orb_breakout_above(test_data, test_idx, {})
        session_result = parser._session_filter(test_data, test_idx, {"value": "london"})
        
        print(f"   ORB condition result: {orb_result}")
        print(f"   Session condition result: {session_result}")
        print(f"   Combined (should be True): {orb_result and session_result}")
        
        # Test pattern function step by step
        print(f"\n🔧 Testing pattern function step by step...")

        # Test entry price calculation
        direction, entry_price = parser._calculate_orb_entry_price(pattern_1.entry_conditions, test_data, test_idx)
        print(f"   Entry price calculation: direction={direction}, price={entry_price}")

        if direction and entry_price:
            # Test stop loss / take profit calculation
            stop_loss, take_profit = parser._calculate_sl_tp(pattern_1, test_data, test_idx, direction, entry_price)
            print(f"   SL/TP calculation: stop_loss={stop_loss}, take_profit={take_profit}")

            if stop_loss is not None and take_profit is not None:
                print(f"   ✅ All calculations successful!")
                print(f"      Entry: {entry_price}")
                print(f"      Stop Loss: {stop_loss}")
                print(f"      Take Profit: {take_profit}")
                print(f"      Direction: {direction}")
            else:
                print(f"   ❌ SL/TP calculation failed!")
                print(f"   Exit conditions: {pattern_1.exit_conditions}")

                # Debug the exit conditions
                for i, exit_condition in enumerate(pattern_1.exit_conditions):
                    print(f"      Exit condition {i+1}: {exit_condition}")
        else:
            print(f"   ❌ Entry price calculation failed!")

        # Now test the full pattern function
        pattern_function = parser._create_python_function(pattern_1)
        if pattern_function:
            print(f"\n🔧 Testing full pattern function...")
            try:
                signal = pattern_function(test_data, test_idx)
                print(f"   Pattern function result: {signal}")

                if signal:
                    print(f"   ✅ SUCCESS! Pattern function generated signal")
                else:
                    print(f"   ❌ Pattern function returned None despite valid components")

            except Exception as e:
                print(f"   ❌ Pattern function error: {e}")
                import traceback
                traceback.print_exc()
        else:
            print(f"   ❌ Failed to create pattern function")
    else:
        print(f"❌ No ORB signal bars found in London session!")

if __name__ == "__main__":
    simple_debug()
