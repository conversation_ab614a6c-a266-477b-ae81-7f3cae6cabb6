#!/usr/bin/env python3
"""
Test script to verify fact checker is working in file generation
"""

import sys
import os
sys.path.append('src')

from file_generator import FileGenerator
from datetime import datetime

def test_fact_checker_in_file_generation():
    """Test that fact checker is called during file generation"""
    
    # Create mock cortex results with LLM analysis that should trigger fact checking
    cortex_results = {
        'symbol': 'TESTEUR',
        'llm_analysis': '''
        PATTERN 1: Morning Volume Surge
        - 85% success rate with 4x volume increase
        - Average volume: 250,000 (this should be fact-checked)
        - Occurs exactly 15 times per month
        - 3.2R average profit ratio
        - Works best at 9:30 AM sharp
        
        PATTERN 2: London Breakout
        - 92% win rate (this is a fabricated metric)
        - Average profit: 2.8R per trade
        - Volume spikes to 500,000 during breakouts
        ''',
        'mt4_ea_code': '// Test EA code',
        'ea_name': 'Test_EA',
        'ohlc_data': None,
        'full_data': None,
        'timeframe_data': None
    }
    
    # Create mock backtest results with at least one "profitable" pattern
    backtest_results = [
        {
            'pattern_name': 'Test Pattern 1',
            'is_profitable': True,  # Force file generation
            'trade_count': 10,
            'total_return': 15.5,
            'backtesting_py_stats': None
        }
    ]
    
    print("🧪 Testing fact checker in file generation...")
    print("📊 Mock LLM analysis contains fabricated metrics that should be caught")
    print("💰 Mock results show profitable pattern to trigger file generation")
    
    # Create file generator and test
    file_gen = FileGenerator()
    
    try:
        generated_files = file_gen.generate_trading_system_files(cortex_results, backtest_results)
        
        print(f"✅ File generation completed")
        print(f"📁 Files generated in: {generated_files.get('system_folder', 'Unknown')}")
        
        # Check if the generated report contains fact-check results
        report_file = generated_files.get('trading_system_report')
        if report_file and os.path.exists(report_file):
            with open(report_file, 'r', encoding='utf-8') as f:
                report_content = f.read()
            
            print(f"📄 Report file: {os.path.basename(report_file)}")
            print(f"📏 Report size: {len(report_content)} characters")
            
            # Check for fact-check indicators
            if "FACT-CHECK RESULTS" in report_content:
                print("✅ Fact-check results found in report!")
                print("🔍 Fact checker is working correctly")
            elif "VALIDATION PASSED" in report_content:
                print("✅ Validation passed message found in report")
                print("🔍 Fact checker ran but found no issues")
            else:
                print("❌ No fact-check indicators found in report")
                print("🔍 Fact checker may not be running")
            
            # Show a preview of the LLM analysis section
            if "LLM PATTERN ANALYSIS" in report_content:
                start = report_content.find("## 🧠 LLM PATTERN ANALYSIS")
                end = report_content.find("---", start + 1)
                if start != -1 and end != -1:
                    analysis_section = report_content[start:end]
                    print("\n📖 LLM Analysis Section Preview:")
                    print("=" * 60)
                    print(analysis_section[:800] + "..." if len(analysis_section) > 800 else analysis_section)
                    print("=" * 60)
        else:
            print("❌ Report file not found or not generated")
            
    except Exception as e:
        print(f"❌ Error during file generation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fact_checker_in_file_generation()
