#!/usr/bin/env python3
"""
Test simple ORB pattern that should definitely generate trades
"""

import sys
import pandas as pd
import json
sys.path.append('src')

from backtesting_rule_parser import BacktestingRuleParser
from behavioral_intelligence import generate_orb_timeframes

def test_simple_orb():
    """Test a very simple ORB pattern"""
    print("🧪 Testing Simple ORB Pattern")
    print("=" * 60)
    
    # Load and prepare data
    print("📊 Loading data...")
    data = pd.read_csv('data/2025.6.23DEUIDXEUR_M1_UTCPlus01-M1-No Session.csv')
    data['DateTime'] = pd.to_datetime(data['DateTime'], format='%Y%m%d %H:%M:%S')
    data.set_index('DateTime', inplace=True)
    
    # Generate ORB timeframes
    orb_timeframes = generate_orb_timeframes(data)
    test_data = orb_timeframes['15min']
    
    print(f"   Test data: {len(test_data)} bars")
    print(f"   ORB UP signals available: {test_data['orb_breakout_up'].sum()}")
    print(f"   ORB DOWN signals available: {test_data['orb_breakout_down'].sum()}")
    
    # Create a VERY simple ORB pattern - just breakout, no other conditions
    simple_pattern = {
        "pattern_name": "Simple ORB Test",
        "description": "Basic ORB breakout with minimal conditions",
        "entry_conditions": [
            {
                "condition": "orb_breakout_above"
            }
        ],
        "entry_logic": "AND",
        "exit_conditions": [
            {
                "condition": "fixed_pips_target",
                "pips": 50
            }
        ],
        "position_sizing": {
            "method": "fixed_percent",
            "value": 0.01
        }
    }
    
    print(f"\n🔧 Testing pattern: {simple_pattern['pattern_name']}")
    print(f"   Entry conditions: {len(simple_pattern['entry_conditions'])}")
    
    # Test with rule parser
    parser = BacktestingRuleParser()
    
    # Test signal generation on a few bars
    signal_count = 0
    test_bars = min(1000, len(test_data))  # Test first 1000 bars
    
    print(f"\n🔍 Testing signal generation on first {test_bars} bars...")
    
    for i in range(10, test_bars):  # Start from bar 10 to have some history
        try:
            signal = parser.evaluate_entry_conditions(simple_pattern, test_data, i)
            if signal:
                signal_count += 1
                if signal_count <= 5:  # Show first 5 signals
                    bar_time = test_data.index[i]
                    close_price = test_data['Close'].iloc[i]
                    orb_high = test_data['opening_range_high'].iloc[i]
                    print(f"   ✅ Signal {signal_count}: {bar_time}, Close={close_price:.2f}, ORB_High={orb_high:.2f}")
        except Exception as e:
            if signal_count == 0:  # Only show first error
                print(f"   ❌ Error at bar {i}: {e}")
    
    print(f"\n📊 Results:")
    print(f"   Total signals generated: {signal_count}/{test_bars}")
    print(f"   Signal rate: {signal_count/test_bars*100:.2f}%")
    
    if signal_count == 0:
        print("\n🔍 Debugging why no signals...")
        
        # Check individual conditions
        for i in range(10, min(100, len(test_data))):
            orb_up = test_data['orb_breakout_up'].iloc[i]
            if orb_up:
                print(f"   Bar {i}: ORB breakout detected but no signal generated")
                # Check what the parser is doing
                try:
                    result = parser._orb_breakout_above(test_data, i, {})
                    print(f"      Parser result: {result}")
                except Exception as e:
                    print(f"      Parser error: {e}")
                break
    
    return signal_count > 0

if __name__ == "__main__":
    success = test_simple_orb()
    if success:
        print("\n🎉 Simple ORB test PASSED!")
    else:
        print("\n❌ Simple ORB test FAILED!")
